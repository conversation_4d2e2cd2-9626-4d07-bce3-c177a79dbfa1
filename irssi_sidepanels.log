signal: irssi init finished -> redraw_all
DEBUG enabled permanently for resize troubleshooting
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=321 width=322 height=84
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d38000
AUTO_HIDE: no active item (probably status window)
AUTO_HIDE: hiding right panel
AUTO_HIDE: final show_right=0
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=83] R[0,0 w=18 h=0]
REDRAW_ONE: mw geometry first_col=0 last_col=321 width=322
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=321 width=322 height=84
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d38000
AUTO_HIDE: no active item (probably status window)
AUTO_HIDE: hiding right panel
AUTO_HIDE: final show_right=0
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=321 width=322 height=84
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=83] R[0,0 w=18 h=0]
REDRAW_ONE: mw geometry first_col=0 last_col=321 width=322
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=321 width=322 height=84
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d38000
AUTO_HIDE: no active item (probably status window)
AUTO_HIDE: hiding right panel
AUTO_HIDE: final show_right=0
MOUSE_KEY: got key=0x2f ('/') mouse_state=0 mouse_len=0 esc_pending=0 reemit_guard=0
MOUSE_KEY: got key=0x6a ('j') mouse_state=0 mouse_len=0 esc_pending=0 reemit_guard=0
MOUSE_KEY: got key=0x20 (' ') mouse_state=0 mouse_len=0 esc_pending=0 reemit_guard=0
MOUSE_KEY: got key=0x69 ('i') mouse_state=0 mouse_len=0 esc_pending=0 reemit_guard=0
MOUSE_KEY: got key=0x72 ('r') mouse_state=0 mouse_len=0 esc_pending=0 reemit_guard=0
MOUSE_KEY: got key=0x63 ('c') mouse_state=0 mouse_len=0 esc_pending=0 reemit_guard=0
MOUSE_KEY: got key=0x2e ('.') mouse_state=0 mouse_len=0 esc_pending=0 reemit_guard=0
MOUSE_KEY: got key=0x61 ('a') mouse_state=0 mouse_len=0 esc_pending=0 reemit_guard=0
MOUSE_KEY: got key=0x6c ('l') mouse_state=0 mouse_len=0 esc_pending=0 reemit_guard=0
MOUSE_KEY: got key=0xd ('?') mouse_state=0 mouse_len=0 esc_pending=0 reemit_guard=0
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=83] R[0,0 w=18 h=0]
REDRAW_ONE: mw geometry first_col=0 last_col=321 width=322
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=321 width=322 height=84
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d38000
AUTO_HIDE: no active item (probably status window)
AUTO_HIDE: hiding right panel
AUTO_HIDE: final show_right=0
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=83] R[0,0 w=18 h=0]
REDRAW_ONE: mw geometry first_col=0 last_col=321 width=322
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=321 width=322 height=84
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=321
RIGHT_PANEL: creating new panel, reserving 18 columns
RIGHT_PANEL: last_column BEFORE reserve: 321
RIGHT_PANEL: last_column AFTER reserve: 303
RIGHT_PANEL: creating term_window at x=304 y=1 w=18 h=83
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=303 width=304 height=84
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=83] R[0,0 w=18 h=0]
REDRAW_ONE: mw geometry first_col=0 last_col=303 width=304
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=303 width=304 height=84
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=303
RIGHT_PANEL: moving existing panel to x=304 (last_col+1=304)
RIGHT_PANEL: final position stored - x=304 y=1 w=18 h=83
REDRAW_ONE: drawing right contents at stored position x=304
RIGHT_PANEL: drawing nicklist for channel #irc.al, 0 nicks found
RIGHT_PANEL: final position stored - x=304 y=1 w=18 h=83
REDRAW_ONE: drawing right contents at stored position x=304
RIGHT_PANEL: drawing nicklist for channel #irc.al, 0 nicks found
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=83] R[304,1 w=18 h=83]
REDRAW_ONE: mw geometry first_col=0 last_col=303 width=304
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=303 width=304 height=84
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=303
RIGHT_PANEL: moving existing panel to x=304 (last_col+1=304)
RIGHT_PANEL: final position stored - x=304 y=1 w=18 h=83
REDRAW_ONE: drawing right contents at stored position x=304
RIGHT_PANEL: drawing nicklist for channel #irc.al, 0 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=303 width=304 height=84
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=83] R[304,1 w=18 h=83]
REDRAW_ONE: mw geometry first_col=0 last_col=303 width=304
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=303 width=304 height=84
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=303
RIGHT_PANEL: moving existing panel to x=304 (last_col+1=304)
RIGHT_PANEL: final position stored - x=304 y=1 w=18 h=83
REDRAW_ONE: drawing right contents at stored position x=304
RIGHT_PANEL: drawing nicklist for channel #irc.al, 0 nicks found
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=83] R[304,1 w=18 h=83]
REDRAW_ONE: mw geometry first_col=0 last_col=303 width=304
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=303 width=304 height=84
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=303
RIGHT_PANEL: moving existing panel to x=304 (last_col+1=304)
RIGHT_PANEL: final position stored - x=304 y=1 w=18 h=83
REDRAW_ONE: drawing right contents at stored position x=304
RIGHT_PANEL: drawing nicklist for channel #irc.al, 1 nicks found
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=83] R[304,1 w=18 h=83]
REDRAW_ONE: mw geometry first_col=0 last_col=303 width=304
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=303 width=304 height=84
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=303
RIGHT_PANEL: moving existing panel to x=304 (last_col+1=304)
RIGHT_PANEL: final position stored - x=304 y=1 w=18 h=83
REDRAW_ONE: drawing right contents at stored position x=304
RIGHT_PANEL: drawing nicklist for channel #irc.al, 2 nicks found
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=83] R[304,1 w=18 h=83]
REDRAW_ONE: mw geometry first_col=0 last_col=303 width=304
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=303 width=304 height=84
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=303
RIGHT_PANEL: moving existing panel to x=304 (last_col+1=304)
RIGHT_PANEL: final position stored - x=304 y=1 w=18 h=83
REDRAW_ONE: drawing right contents at stored position x=304
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=280 width=281 height=85
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=83] R[304,1 w=18 h=83]
REDRAW_ONE: mw geometry first_col=0 last_col=280 width=281
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=280 width=281 height=85
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=280
RIGHT_PANEL: moving existing panel to x=281 (last_col+1=281)
RIGHT_PANEL: final position stored - x=281 y=1 w=18 h=84
REDRAW_ONE: drawing right contents at stored position x=281
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=264 width=265 height=86
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=84] R[281,1 w=18 h=84]
REDRAW_ONE: mw geometry first_col=0 last_col=264 width=265
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=264 width=265 height=86
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=264
RIGHT_PANEL: moving existing panel to x=265 (last_col+1=265)
RIGHT_PANEL: final position stored - x=265 y=1 w=18 h=85
REDRAW_ONE: drawing right contents at stored position x=265
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=257 width=258 height=87
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=85] R[265,1 w=18 h=85]
REDRAW_ONE: mw geometry first_col=0 last_col=257 width=258
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=257 width=258 height=87
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=257
RIGHT_PANEL: moving existing panel to x=258 (last_col+1=258)
RIGHT_PANEL: final position stored - x=258 y=1 w=18 h=86
REDRAW_ONE: drawing right contents at stored position x=258
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=282 width=283 height=87
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=86] R[258,1 w=18 h=86]
REDRAW_ONE: mw geometry first_col=0 last_col=282 width=283
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=282 width=283 height=87
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=282
RIGHT_PANEL: moving existing panel to x=283 (last_col+1=283)
RIGHT_PANEL: final position stored - x=283 y=1 w=18 h=86
REDRAW_ONE: drawing right contents at stored position x=283
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=321 width=322 height=89
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=86] R[283,1 w=18 h=86]
REDRAW_ONE: mw geometry first_col=0 last_col=321 width=322
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=321 width=322 height=89
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=321
RIGHT_PANEL: moving existing panel to x=322 (last_col+1=322)
RIGHT_PANEL: final position stored - x=322 y=1 w=18 h=88
REDRAW_ONE: drawing right contents at stored position x=322
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=358 width=359 height=91
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=88] R[322,1 w=18 h=88]
REDRAW_ONE: mw geometry first_col=0 last_col=358 width=359
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=358 width=359 height=91
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=358
RIGHT_PANEL: moving existing panel to x=359 (last_col+1=359)
RIGHT_PANEL: final position stored - x=359 y=1 w=18 h=90
REDRAW_ONE: drawing right contents at stored position x=359
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=333 width=334 height=95
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=90] R[359,1 w=18 h=90]
REDRAW_ONE: mw geometry first_col=0 last_col=333 width=334
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=333 width=334 height=95
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=333
RIGHT_PANEL: moving existing panel to x=334 (last_col+1=334)
RIGHT_PANEL: final position stored - x=334 y=1 w=18 h=94
REDRAW_ONE: drawing right contents at stored position x=334
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=322 width=323 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=94] R[334,1 w=18 h=94]
REDRAW_ONE: mw geometry first_col=0 last_col=322 width=323
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=322 width=323 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=322
RIGHT_PANEL: moving existing panel to x=323 (last_col+1=323)
RIGHT_PANEL: final position stored - x=323 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=323
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=313 width=314 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[323,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=313 width=314
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=313 width=314 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=313
RIGHT_PANEL: moving existing panel to x=314 (last_col+1=314)
RIGHT_PANEL: final position stored - x=314 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=314
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=268 width=269 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[314,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=268 width=269
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=268 width=269 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=268
RIGHT_PANEL: moving existing panel to x=269 (last_col+1=269)
RIGHT_PANEL: final position stored - x=269 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=269
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=245 width=246 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[269,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=245 width=246
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=245 width=246 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=245
RIGHT_PANEL: moving existing panel to x=246 (last_col+1=246)
RIGHT_PANEL: final position stored - x=246 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=246
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=205 width=206 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[246,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=205 width=206
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=205 width=206 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=205
RIGHT_PANEL: moving existing panel to x=206 (last_col+1=206)
RIGHT_PANEL: final position stored - x=206 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=206
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=179 width=180 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[206,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=179 width=180
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=179 width=180 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=179
RIGHT_PANEL: moving existing panel to x=180 (last_col+1=180)
RIGHT_PANEL: final position stored - x=180 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=180
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=161 width=162 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[180,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=161 width=162
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=161 width=162 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=161
RIGHT_PANEL: moving existing panel to x=162 (last_col+1=162)
RIGHT_PANEL: final position stored - x=162 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=162
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=140 width=141 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[162,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=140 width=141
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=140 width=141 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=140
RIGHT_PANEL: moving existing panel to x=141 (last_col+1=141)
RIGHT_PANEL: final position stored - x=141 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=141
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=115 width=116 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[141,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=115 width=116
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=115 width=116 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=115
RIGHT_PANEL: moving existing panel to x=116 (last_col+1=116)
RIGHT_PANEL: final position stored - x=116 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=116
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=98 width=99 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[116,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=98 width=99
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=98 width=99 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=98
RIGHT_PANEL: moving existing panel to x=99 (last_col+1=99)
RIGHT_PANEL: final position stored - x=99 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=99
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=82 width=83 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[99,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=82 width=83
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=82 width=83 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=82
RIGHT_PANEL: moving existing panel to x=83 (last_col+1=83)
RIGHT_PANEL: final position stored - x=83 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=83
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=51 width=52 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[83,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=51 width=52
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=51 width=52 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=51
RIGHT_PANEL: moving existing panel to x=52 (last_col+1=52)
RIGHT_PANEL: final position stored - x=52 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=52
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=48 width=49 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[52,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=48 width=49
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=48 width=49 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=48
RIGHT_PANEL: moving existing panel to x=49 (last_col+1=49)
RIGHT_PANEL: final position stored - x=49 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=49
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
RESIZE_SIGNAL: mw=0x6000018381e0 first_col=0 last_col=37 width=38 height=96
REDRAW_ONE: mainwin=0x6000018381e0 L[0,1 w=18 h=95] R[49,1 w=18 h=95]
REDRAW_ONE: mw geometry first_col=0 last_col=37 width=38
POSITION_START: mw=0x6000018381e0 first_col=0 last_col=37 width=38 height=96
AUTO_HIDE: sp_auto_hide_right=1 sp_enable_right=1 aw=0x600000d20000
AUTO_HIDE: active_item_name='#irc.al' contains_hash=1
AUTO_HIDE: final show_right=1
RIGHT_PANEL: show_right=TRUE w=18 last_col_before=37
RIGHT_PANEL: moving existing panel to x=38 (last_col+1=38)
RIGHT_PANEL: final position stored - x=38 y=1 w=18 h=95
REDRAW_ONE: drawing right contents at stored position x=38
RIGHT_PANEL: drawing nicklist for channel #irc.al, 3 nicks found
