#!/bin/bash

# Skrypt do przebudowy irssi
# Użycie: ./rebuild.sh <ścieżka_projektu>

set -e  # Zatrzymaj na pierwszym błędzie

# Kolory dla ładnego outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Funkcja do wyświetlania informacji
info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUKCES]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[UWAGA]${NC} $1"
}

error() {
    echo -e "${RED}[BŁĄD]${NC} $1"
}

step() {
    echo -e "\n${CYAN}==== $1 ====${NC}"
}

# Sprawdzenie argumentu
if [ $# -ne 1 ]; then
    error "Brak ścieżki projektu!"
    echo "Użycie: $0 <ścieżka_projektu>"
    exit 1
fi

PROJECT_PATH="$1"

# Sprawdzenie czy ścieżka istnieje
if [ ! -d "$PROJECT_PATH" ]; then
    error "Ścieżka $PROJECT_PATH nie istnieje!"
    exit 1
fi

info "Rozpoczynam przebudowę irssi w: $PROJECT_PATH"
info "Data rozpoczęcia: $(date '+%Y-%m-%d %H:%M:%S')"

# Krok 1: Czyszczenie
step "CZYSZCZENIE PLIKÓW TYMCZASOWYCH"
info "Usuwam logi irssi z /tmp..."
rm -rf /tmp/irssi*.log
success "Logi irssi usunięte"

info "Usuwam katalog Build..."
if [ -d "$PROJECT_PATH/Build" ]; then
    rm -rf "$PROJECT_PATH/Build"
    success "Katalog Build usunięty"
else
    warning "Katalog Build nie istniał"
fi

info "Usuwam katalog inst..."
if [ -d "$PROJECT_PATH/inst" ]; then
    rm -rf "$PROJECT_PATH/inst"
    success "Katalog inst usunięty"
else
    warning "Katalog inst nie istniał"
fi

# Krok 2: Konfiguracja
step "KONFIGURACJA MESON"
info "Konfiguruję projekt z meson..."
meson setup "$PROJECT_PATH/Build" \
    -Dprefix="$PROJECT_PATH/inst" \
    -Dwith-perl=yes \
    -Dwith-proxy=yes

if [ $? -eq 0 ]; then
    success "Konfiguracja meson zakończona pomyślnie"
else
    error "Błąd podczas konfiguracji meson"
    exit 1
fi

# Krok 3: Kompilacja
step "KOMPILACJA"
info "Rozpoczynam kompilację z ninja..."
ninja -C "$PROJECT_PATH/Build"

if [ $? -eq 0 ]; then
    success "Kompilacja zakończona pomyślnie"
else
    error "Błąd podczas kompilacji"
    exit 1
fi

# Krok 4: Instalacja
step "INSTALACJA"
info "Instaluję skompilowane pliki..."
ninja -C "$PROJECT_PATH/Build" install

if [ $? -eq 0 ]; then
    success "Instalacja zakończona pomyślnie"
else
    error "Błąd podczas instalacji"
    exit 1
fi

# Krok 5: Podsumowanie
step "PODSUMOWANIE"
success "Kompilacja wykonana: $(date '+%Y-%m-%d %H:%M:%S')"

# Sprawdzenie czy plik wykonywalny istnieje
if [ -f "$PROJECT_PATH/inst/bin/irssi" ]; then
    success "Plik wykonywalny irssi został utworzony: $PROJECT_PATH/inst/bin/irssi"
    
    # Opcjonalne uruchomienie testowe
    echo ""
    warning "Za 3 sekundy uruchomię testowo irssi..."
    warning "Aby anulować, naciśnij Ctrl+C"
    echo ""
    
    sleep 3
    
    step "URUCHAMIAM IRSSI TESTOWO"
    info "Uruchamiam: $PROJECT_PATH/inst/bin/irssi --home=/tmp/irssi/ -c 51.195.103.145"
    
    "$PROJECT_PATH/inst/bin/irssi" --home=/tmp/irssi/ -c 51.195.103.145
    
    echo ""
    info "Zakończono: $(date '+%Y-%m-%d %H:%M:%S')"
else
    error "Nie można znaleźć pliku wykonywalnego irssi!"
    exit 1
fi
